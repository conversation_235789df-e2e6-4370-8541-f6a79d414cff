[{"D:\\Documents\\converter\\converter\\src\\app\\layout.tsx": "1", "D:\\Documents\\converter\\converter\\src\\app\\page.tsx": "2", "D:\\Documents\\converter\\converter\\src\\components\\FileConverter.tsx": "3"}, {"size": 795, "mtime": 1753137259210, "results": "4", "hashOfConfig": "5"}, {"size": 194, "mtime": 1753137238953, "results": "6", "hashOfConfig": "5"}, {"size": 10637, "mtime": 1753137216773, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qc8z08", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Documents\\converter\\converter\\src\\app\\layout.tsx", [], [], "D:\\Documents\\converter\\converter\\src\\app\\page.tsx", [], [], "D:\\Documents\\converter\\converter\\src\\components\\FileConverter.tsx", ["17"], [], {"ruleId": "18", "severity": 1, "message": "19", "line": 222, "column": 19, "nodeType": "20", "endLine": 226, "endColumn": 21}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]