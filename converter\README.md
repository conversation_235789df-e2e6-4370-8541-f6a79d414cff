# File Converter

A modern, web-based file converter built with Next.js that allows you to convert various file formats easily.

## Features

- **Image Conversion**: Convert between PNG, JPG, WebP, and GIF formats
- **Document Conversion**: Convert between TXT, HTML, Markdown, and PDF formats
- **Audio Conversion**: Support for MP3, WAV, and OGG formats
- **Video Conversion**: Basic support for MP4, WebM, and AVI formats
- **Drag & Drop Interface**: Easy file upload with drag and drop functionality
- **Batch Processing**: Convert multiple files at once
- **Real-time Preview**: See file information and previews before conversion
- **Download Management**: Download individual files or all converted files at once

## Getting Started

First, install the dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3001](http://localhost:3001) with your browser to see the file converter.

## How to Use

1. **Upload Files**: Drag and drop files onto the upload area or click to select files
2. **Choose Format**: Select the target format you want to convert to
3. **Convert**: Click the "Convert Files" button to start the conversion process
4. **Download**: Download individual files or use "Download All" for batch download

## Supported File Types

### Images
- Input: PNG, JPG, JPEG, GIF, BMP, WebP
- Output: PNG, JPG, WebP, GIF

### Documents
- Input: TXT, MD, HTML, CSS, JS, JSON, PDF
- Output: TXT, HTML, MD, PDF

### Audio
- Input: MP3, WAV, OGG
- Output: MP3, WAV, OGG

### Video
- Input: MP4, WebM, AVI
- Output: MP4, WebM, AVI

## Technologies Used

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **React Dropzone**: File upload with drag & drop
- **Lucide React**: Beautiful icons
- **File Saver**: Client-side file downloads
- **HTML2Canvas**: Canvas-based image conversion

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
