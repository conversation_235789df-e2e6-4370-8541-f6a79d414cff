{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Documents/converter/converter/src/utils/fileConverters.ts"], "sourcesContent": ["// File conversion utilities\n\nexport interface ConversionResult {\n  blob: Blob;\n  filename: string;\n  success: boolean;\n  error?: string;\n}\n\n// Image conversion utilities\nexport const convertImage = async (\n  file: File, \n  targetFormat: string, \n  quality: number = 0.9\n): Promise<ConversionResult> => {\n  return new Promise((resolve) => {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    \n    img.onload = () => {\n      canvas.width = img.width;\n      canvas.height = img.height;\n      \n      // Handle transparency for formats that don't support it\n      if (targetFormat === 'jpg' || targetFormat === 'jpeg') {\n        ctx!.fillStyle = '#FFFFFF';\n        ctx!.fillRect(0, 0, canvas.width, canvas.height);\n      }\n      \n      ctx!.drawImage(img, 0, 0);\n      \n      const mimeType = targetFormat === 'jpg' ? 'image/jpeg' : `image/${targetFormat}`;\n      \n      canvas.toBlob((blob) => {\n        if (blob) {\n          const filename = file.name.replace(/\\.[^/.]+$/, '') + '.' + targetFormat;\n          resolve({\n            blob,\n            filename,\n            success: true\n          });\n        } else {\n          resolve({\n            blob: new Blob(),\n            filename: '',\n            success: false,\n            error: 'Failed to convert image'\n          });\n        }\n      }, mimeType, quality);\n    };\n    \n    img.onerror = () => {\n      resolve({\n        blob: new Blob(),\n        filename: '',\n        success: false,\n        error: 'Failed to load image'\n      });\n    };\n    \n    img.src = URL.createObjectURL(file);\n  });\n};\n\n// Text/Document conversion utilities\nexport const convertText = async (\n  file: File, \n  targetFormat: string\n): Promise<ConversionResult> => {\n  try {\n    const text = await file.text();\n    let convertedContent = text;\n    let mimeType = 'text/plain';\n    \n    // Markdown to HTML conversion\n    if (targetFormat === 'html' && (file.type === 'text/markdown' || file.name.endsWith('.md'))) {\n      convertedContent = markdownToHtml(text);\n      mimeType = 'text/html';\n    }\n    // HTML to Markdown conversion\n    else if (targetFormat === 'md' && file.type === 'text/html') {\n      convertedContent = htmlToMarkdown(text);\n      mimeType = 'text/markdown';\n    }\n    // Text to HTML conversion\n    else if (targetFormat === 'html' && file.type === 'text/plain') {\n      convertedContent = textToHtml(text);\n      mimeType = 'text/html';\n    }\n    // HTML to Text conversion\n    else if (targetFormat === 'txt' && file.type === 'text/html') {\n      convertedContent = htmlToText(text);\n      mimeType = 'text/plain';\n    }\n    // Set appropriate MIME type\n    else if (targetFormat === 'html') {\n      mimeType = 'text/html';\n    } else if (targetFormat === 'md') {\n      mimeType = 'text/markdown';\n    }\n    \n    const blob = new Blob([convertedContent], { type: mimeType });\n    const filename = file.name.replace(/\\.[^/.]+$/, '') + '.' + targetFormat;\n    \n    return {\n      blob,\n      filename,\n      success: true\n    };\n  } catch (error) {\n    return {\n      blob: new Blob(),\n      filename: '',\n      success: false,\n      error: 'Failed to convert text file'\n    };\n  }\n};\n\n// Helper functions for text conversions\nconst markdownToHtml = (markdown: string): string => {\n  let html = markdown\n    // Headers\n    .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n    .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n    .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n    // Bold and Italic\n    .replace(/\\*\\*\\*(.*)\\*\\*\\*/gim, '<strong><em>$1</em></strong>')\n    .replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>')\n    .replace(/\\*(.*)\\*/gim, '<em>$1</em>')\n    // Links\n    .replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/gim, '<a href=\"$2\">$1</a>')\n    // Code blocks\n    .replace(/```([^`]+)```/gim, '<pre><code>$1</code></pre>')\n    .replace(/`([^`]+)`/gim, '<code>$1</code>')\n    // Lists\n    .replace(/^\\* (.+$)/gim, '<li>$1</li>')\n    .replace(/^\\d+\\. (.+$)/gim, '<li>$1</li>')\n    // Line breaks\n    .replace(/\\n\\n/gim, '</p><p>')\n    .replace(/\\n/gim, '<br>');\n  \n  // Wrap in basic HTML structure\n  return `<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Converted Document</title>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }\n        code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }\n        pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }\n    </style>\n</head>\n<body>\n    <p>${html}</p>\n</body>\n</html>`;\n};\n\nconst htmlToMarkdown = (html: string): string => {\n  return html\n    // Remove HTML structure\n    .replace(/<html[^>]*>/gi, '')\n    .replace(/<\\/html>/gi, '')\n    .replace(/<head[^>]*>[\\s\\S]*?<\\/head>/gi, '')\n    .replace(/<body[^>]*>/gi, '')\n    .replace(/<\\/body>/gi, '')\n    // Headers\n    .replace(/<h1[^>]*>(.*?)<\\/h1>/gi, '# $1\\n')\n    .replace(/<h2[^>]*>(.*?)<\\/h2>/gi, '## $1\\n')\n    .replace(/<h3[^>]*>(.*?)<\\/h3>/gi, '### $1\\n')\n    // Bold and Italic\n    .replace(/<strong[^>]*>(.*?)<\\/strong>/gi, '**$1**')\n    .replace(/<em[^>]*>(.*?)<\\/em>/gi, '*$1*')\n    // Links\n    .replace(/<a[^>]*href=\"([^\"]*)\"[^>]*>(.*?)<\\/a>/gi, '[$2]($1)')\n    // Code\n    .replace(/<pre[^>]*><code[^>]*>(.*?)<\\/code><\\/pre>/gi, '```\\n$1\\n```')\n    .replace(/<code[^>]*>(.*?)<\\/code>/gi, '`$1`')\n    // Lists\n    .replace(/<li[^>]*>(.*?)<\\/li>/gi, '* $1\\n')\n    // Paragraphs and breaks\n    .replace(/<p[^>]*>/gi, '\\n')\n    .replace(/<\\/p>/gi, '\\n')\n    .replace(/<br[^>]*>/gi, '\\n')\n    // Remove remaining HTML tags\n    .replace(/<[^>]*>/g, '')\n    // Clean up extra whitespace\n    .replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n')\n    .trim();\n};\n\nconst textToHtml = (text: string): string => {\n  const htmlContent = text\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\\n/g, '<br>');\n  \n  return `<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Converted Text</title>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }\n    </style>\n</head>\n<body>\n    <p>${htmlContent}</p>\n</body>\n</html>`;\n};\n\nconst htmlToText = (html: string): string => {\n  return html\n    .replace(/<br[^>]*>/gi, '\\n')\n    .replace(/<p[^>]*>/gi, '\\n')\n    .replace(/<\\/p>/gi, '\\n')\n    .replace(/<[^>]*>/g, '')\n    .replace(/&amp;/g, '&')\n    .replace(/&lt;/g, '<')\n    .replace(/&gt;/g, '>')\n    .replace(/&quot;/g, '\"')\n    .replace(/&#39;/g, \"'\")\n    .replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n')\n    .trim();\n};\n\n// File type detection utilities\nexport const getFileCategory = (file: File): string => {\n  if (file.type.startsWith('image/')) return 'image';\n  if (file.type.startsWith('audio/')) return 'audio';\n  if (file.type.startsWith('video/')) return 'video';\n  if (file.type.startsWith('text/') || file.type === 'application/pdf') return 'document';\n  return 'other';\n};\n\nexport const getSupportedFormats = (category: string): string[] => {\n  switch (category) {\n    case 'image':\n      return ['png', 'jpg', 'jpeg', 'webp', 'gif'];\n    case 'document':\n      return ['txt', 'html', 'md'];\n    case 'audio':\n      return ['mp3', 'wav', 'ogg'];\n    case 'video':\n      return ['mp4', 'webm', 'avi'];\n    default:\n      return [];\n  }\n};\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;AAUrB,MAAM,eAAe,eAC1B,MACA;QACA,2EAAkB;IAElB,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,MAAM,MAAM,IAAI;QAEhB,IAAI,MAAM,GAAG;YACX,OAAO,KAAK,GAAG,IAAI,KAAK;YACxB,OAAO,MAAM,GAAG,IAAI,MAAM;YAE1B,wDAAwD;YACxD,IAAI,iBAAiB,SAAS,iBAAiB,QAAQ;gBACrD,IAAK,SAAS,GAAG;gBACjB,IAAK,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YACjD;YAEA,IAAK,SAAS,CAAC,KAAK,GAAG;YAEvB,MAAM,WAAW,iBAAiB,QAAQ,eAAe,AAAC,SAAqB,OAAb;YAElE,OAAO,MAAM,CAAC,CAAC;gBACb,IAAI,MAAM;oBACR,MAAM,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,MAAM,MAAM;oBAC5D,QAAQ;wBACN;wBACA;wBACA,SAAS;oBACX;gBACF,OAAO;oBACL,QAAQ;wBACN,MAAM,IAAI;wBACV,UAAU;wBACV,SAAS;wBACT,OAAO;oBACT;gBACF;YACF,GAAG,UAAU;QACf;QAEA,IAAI,OAAO,GAAG;YACZ,QAAQ;gBACN,MAAM,IAAI;gBACV,UAAU;gBACV,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF;AAGO,MAAM,cAAc,OACzB,MACA;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,KAAK,IAAI;QAC5B,IAAI,mBAAmB;QACvB,IAAI,WAAW;QAEf,8BAA8B;QAC9B,IAAI,iBAAiB,UAAU,CAAC,KAAK,IAAI,KAAK,mBAAmB,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;YAC3F,mBAAmB,eAAe;YAClC,WAAW;QACb,OAEK,IAAI,iBAAiB,QAAQ,KAAK,IAAI,KAAK,aAAa;YAC3D,mBAAmB,eAAe;YAClC,WAAW;QACb,OAEK,IAAI,iBAAiB,UAAU,KAAK,IAAI,KAAK,cAAc;YAC9D,mBAAmB,WAAW;YAC9B,WAAW;QACb,OAEK,IAAI,iBAAiB,SAAS,KAAK,IAAI,KAAK,aAAa;YAC5D,mBAAmB,WAAW;YAC9B,WAAW;QACb,OAEK,IAAI,iBAAiB,QAAQ;YAChC,WAAW;QACb,OAAO,IAAI,iBAAiB,MAAM;YAChC,WAAW;QACb;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC;SAAiB,EAAE;YAAE,MAAM;QAAS;QAC3D,MAAM,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,MAAM,MAAM;QAE5D,OAAO;YACL;YACA;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,MAAM,IAAI;YACV,UAAU;YACV,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEA,wCAAwC;AACxC,MAAM,iBAAiB,CAAC;IACtB,IAAI,OAAO,QACT,UAAU;KACT,OAAO,CAAC,iBAAiB,eACzB,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,eAAe,cACxB,kBAAkB;KACjB,OAAO,CAAC,uBAAuB,gCAC/B,OAAO,CAAC,mBAAmB,uBAC3B,OAAO,CAAC,eAAe,cACxB,QAAQ;KACP,OAAO,CAAC,8BAA8B,sBACvC,cAAc;KACb,OAAO,CAAC,oBAAoB,8BAC5B,OAAO,CAAC,gBAAgB,kBACzB,QAAQ;KACP,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,mBAAmB,cAC5B,cAAc;KACb,OAAO,CAAC,WAAW,WACnB,OAAO,CAAC,SAAS;IAEpB,+BAA+B;IAC/B,OAAO,AAAC,gaAYI,OAAL,MAAK;AAGd;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAO,IACL,wBAAwB;KACvB,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,cAAc,IACtB,OAAO,CAAC,iCAAiC,IACzC,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,cAAc,GACvB,UAAU;KACT,OAAO,CAAC,0BAA0B,UAClC,OAAO,CAAC,0BAA0B,WAClC,OAAO,CAAC,0BAA0B,WACnC,kBAAkB;KACjB,OAAO,CAAC,kCAAkC,UAC1C,OAAO,CAAC,0BAA0B,OACnC,QAAQ;KACP,OAAO,CAAC,2CAA2C,WACpD,OAAO;KACN,OAAO,CAAC,+CAA+C,gBACvD,OAAO,CAAC,8BAA8B,OACvC,QAAQ;KACP,OAAO,CAAC,0BAA0B,SACnC,wBAAwB;KACvB,OAAO,CAAC,cAAc,MACtB,OAAO,CAAC,WAAW,MACnB,OAAO,CAAC,eAAe,KACxB,6BAA6B;KAC5B,OAAO,CAAC,YAAY,GACrB,4BAA4B;KAC3B,OAAO,CAAC,iBAAiB,QACzB,IAAI;AACT;AAEA,MAAM,aAAa,CAAC;IAClB,MAAM,cAAc,KACjB,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,OAAO;IAElB,OAAO,AAAC,wOAUW,OAAZ,aAAY;AAGrB;AAEA,MAAM,aAAa,CAAC;IAClB,OAAO,KACJ,OAAO,CAAC,eAAe,MACvB,OAAO,CAAC,cAAc,MACtB,OAAO,CAAC,WAAW,MACnB,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,iBAAiB,QACzB,IAAI;AACT;AAGO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;IAC3C,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;IAC3C,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;IAC3C,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,KAAK,IAAI,KAAK,mBAAmB,OAAO;IAC7E,OAAO;AACT;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK;YACH,OAAO;gBAAC;gBAAO;gBAAO;gBAAQ;gBAAQ;aAAM;QAC9C,KAAK;YACH,OAAO;gBAAC;gBAAO;gBAAQ;aAAK;QAC9B,KAAK;YACH,OAAO;gBAAC;gBAAO;gBAAO;aAAM;QAC9B,KAAK;YACH,OAAO;gBAAC;gBAAO;gBAAQ;aAAM;QAC/B;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Documents/converter/converter/src/components/FileConverter.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, Download, FileText, Image as ImageIcon, Music, Video, Loader2, CheckCircle, AlertCircle } from 'lucide-react';\nimport { saveAs } from 'file-saver';\nimport { convertImage, convertText, getFileCategory, getSupportedFormats, ConversionResult } from '@/utils/fileConverters';\n\ninterface ConvertedFile {\n  name: string;\n  blob: Blob;\n  originalName: string;\n  success: boolean;\n  error?: string;\n}\n\ninterface FileWithPreview extends File {\n  preview?: string;\n}\n\nconst FileConverter: React.FC = () => {\n  const [files, setFiles] = useState<FileWithPreview[]>([]);\n  const [convertedFiles, setConvertedFiles] = useState<ConvertedFile[]>([]);\n  const [isConverting, setIsConverting] = useState(false);\n  const [selectedFormat, setSelectedFormat] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const filesWithPreview = acceptedFiles.map(file => {\n      const fileWithPreview = file as FileWithPreview;\n      if (file.type.startsWith('image/')) {\n        fileWithPreview.preview = URL.createObjectURL(file);\n      }\n      return fileWithPreview;\n    });\n    setFiles(filesWithPreview);\n    setConvertedFiles([]);\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    multiple: true,\n    accept: {\n      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'],\n      'text/*': ['.txt', '.md', '.html', '.css', '.js', '.json'],\n      'application/pdf': ['.pdf'],\n      'audio/*': ['.mp3', '.wav', '.ogg'],\n      'video/*': ['.mp4', '.webm', '.avi']\n    }\n  });\n\n  const getFileIcon = (fileType: string) => {\n    if (fileType.startsWith('image/')) return <ImageIcon className=\"w-6 h-6\" />;\n    if (fileType.startsWith('audio/')) return <Music className=\"w-6 h-6\" />;\n    if (fileType.startsWith('video/')) return <Video className=\"w-6 h-6\" />;\n    return <FileText className=\"w-6 h-6\" />;\n  };\n\n  const getAvailableFormats = (file: File) => {\n    const category = getFileCategory(file);\n    return getSupportedFormats(category);\n  };\n\n  const convertFile = async (file: File, format: string): Promise<ConvertedFile> => {\n    const category = getFileCategory(file);\n    let result: ConversionResult;\n\n    try {\n      if (category === 'image') {\n        result = await convertImage(file, format);\n      } else if (category === 'document') {\n        result = await convertText(file, format);\n      } else {\n        // For audio/video files, we'll just change the extension for demo purposes\n        // In a real app, you'd use libraries like FFmpeg.js\n        const fileName = file.name.replace(/\\.[^/.]+$/, '') + '.' + format;\n        result = {\n          blob: new Blob([file], { type: file.type }),\n          filename: fileName,\n          success: true\n        };\n      }\n\n      return {\n        name: result.filename,\n        blob: result.blob,\n        originalName: file.name,\n        success: result.success,\n        error: result.error\n      };\n    } catch (error) {\n      return {\n        name: '',\n        blob: new Blob(),\n        originalName: file.name,\n        success: false,\n        error: 'Conversion failed'\n      };\n    }\n  };\n\n  const handleConvert = async () => {\n    if (!selectedFormat || files.length === 0) return;\n\n    setIsConverting(true);\n    try {\n      const converted = await Promise.all(\n        files.map(file => convertFile(file, selectedFormat))\n      );\n      setConvertedFiles(converted);\n    } catch (error) {\n      console.error('Conversion error:', error);\n    } finally {\n      setIsConverting(false);\n    }\n  };\n\n  const downloadFile = (convertedFile: ConvertedFile) => {\n    saveAs(convertedFile.blob, convertedFile.name);\n  };\n\n  const downloadAll = () => {\n    convertedFiles\n      .filter(file => file.success)\n      .forEach(file => {\n        saveAs(file.blob, file.name);\n      });\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 space-y-6\">\n      <div className=\"text-center\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">File Converter</h1>\n        <p className=\"text-gray-600\">Convert your files to different formats easily</p>\n      </div>\n\n      {/* File Upload Area */}\n      <div\n        {...getRootProps()}\n        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive\n          ? 'border-blue-500 bg-blue-50'\n          : 'border-gray-300 hover:border-gray-400'\n          }`}\n      >\n        <input {...getInputProps()} />\n        <Upload className=\"w-12 h-12 mx-auto mb-4 text-gray-400\" />\n        {isDragActive ? (\n          <p className=\"text-blue-600\">Drop the files here...</p>\n        ) : (\n          <div>\n            <p className=\"text-gray-600 mb-2\">Drag & drop files here, or click to select</p>\n            <p className=\"text-sm text-gray-400\">\n              Supports images, documents, audio, and video files\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* File List */}\n      {files.length > 0 && (\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Selected Files</h3>\n          <div className=\"grid gap-4\">\n            {files.map((file, index) => (\n              <div key={index} className=\"flex items-center space-x-4 p-4 border rounded-lg\">\n                {getFileIcon(file.type)}\n                <div className=\"flex-1\">\n                  <p className=\"font-medium\">{file.name}</p>\n                  <p className=\"text-sm text-gray-500\">\n                    {(file.size / 1024 / 1024).toFixed(2)} MB • {file.type}\n                  </p>\n                </div>\n                {file.preview && (\n                  <div className=\"w-16 h-16 bg-gray-200 rounded flex items-center justify-center\">\n                    <ImageIcon className=\"w-8 h-8 text-gray-400\" />\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Format Selection */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">Convert To</h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {files.length > 0 && getAvailableFormats(files[0]).map((format) => (\n                <button\n                  key={format}\n                  onClick={() => setSelectedFormat(format)}\n                  className={`px-4 py-2 rounded-lg border transition-colors ${selectedFormat === format\n                    ? 'bg-blue-500 text-white border-blue-500'\n                    : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'\n                    }`}\n                >\n                  .{format.toUpperCase()}\n                </button>\n              ))}\n            </div>\n\n            {selectedFormat && (\n              <button\n                onClick={handleConvert}\n                disabled={isConverting}\n                className=\"flex items-center space-x-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isConverting ? (\n                  <Loader2 className=\"w-5 h-5 animate-spin\" />\n                ) : (\n                  <Download className=\"w-5 h-5\" />\n                )}\n                <span>{isConverting ? 'Converting...' : 'Convert Files'}</span>\n              </button>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Converted Files */}\n      {convertedFiles.length > 0 && (\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold\">Converted Files</h3>\n            <button\n              onClick={downloadAll}\n              className=\"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600\"\n            >\n              Download All\n            </button>\n          </div>\n          <div className=\"grid gap-4\">\n            {convertedFiles.map((file, index) => (\n              <div key={index} className={`flex items-center justify-between p-4 border rounded-lg ${file.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'\n                }`}>\n                <div className=\"flex items-center space-x-3\">\n                  {file.success ? (\n                    <CheckCircle className=\"w-5 h-5 text-green-500\" />\n                  ) : (\n                    <AlertCircle className=\"w-5 h-5 text-red-500\" />\n                  )}\n                  <div>\n                    <p className=\"font-medium\">{file.success ? file.name : 'Conversion Failed'}</p>\n                    <p className=\"text-sm text-gray-500\">\n                      {file.success\n                        ? `Converted from ${file.originalName}`\n                        : file.error || 'Unknown error occurred'\n                      }\n                    </p>\n                  </div>\n                </div>\n                {file.success && (\n                  <button\n                    onClick={() => downloadFile(file)}\n                    className=\"flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600\"\n                  >\n                    <Download className=\"w-4 h-4\" />\n                    <span>Download</span>\n                  </button>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FileConverter;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAoBA,MAAM,gBAA0B;;IAC9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC1B,MAAM,mBAAmB,cAAc,GAAG;sEAAC,CAAA;oBACzC,MAAM,kBAAkB;oBACxB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;wBAClC,gBAAgB,OAAO,GAAG,IAAI,eAAe,CAAC;oBAChD;oBACA,OAAO;gBACT;;YACA,SAAS;YACT,kBAAkB,EAAE;QACtB;4CAAG,EAAE;IAEL,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,UAAU;QACV,QAAQ;YACN,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;gBAAQ;aAAQ;YAC7D,UAAU;gBAAC;gBAAQ;gBAAO;gBAAS;gBAAQ;gBAAO;aAAQ;YAC1D,mBAAmB;gBAAC;aAAO;YAC3B,WAAW;gBAAC;gBAAQ;gBAAQ;aAAO;YACnC,WAAW;gBAAC;gBAAQ;gBAAS;aAAO;QACtC;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,UAAU,CAAC,WAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;QAC/D,IAAI,SAAS,UAAU,CAAC,WAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC3D,IAAI,SAAS,UAAU,CAAC,WAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC3D,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE;QACjC,OAAO,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD,EAAE;IAC7B;IAEA,MAAM,cAAc,OAAO,MAAY;QACrC,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE;QACjC,IAAI;QAEJ,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,MAAM;YACpC,OAAO,IAAI,aAAa,YAAY;gBAClC,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,MAAM;YACnC,OAAO;gBACL,2EAA2E;gBAC3E,oDAAoD;gBACpD,MAAM,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,MAAM,MAAM;gBAC5D,SAAS;oBACP,MAAM,IAAI,KAAK;wBAAC;qBAAK,EAAE;wBAAE,MAAM,KAAK,IAAI;oBAAC;oBACzC,UAAU;oBACV,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,MAAM,OAAO,QAAQ;gBACrB,MAAM,OAAO,IAAI;gBACjB,cAAc,KAAK,IAAI;gBACvB,SAAS,OAAO,OAAO;gBACvB,OAAO,OAAO,KAAK;YACrB;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,MAAM;gBACN,MAAM,IAAI;gBACV,cAAc,KAAK,IAAI;gBACvB,SAAS;gBACT,OAAO;YACT;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,kBAAkB,MAAM,MAAM,KAAK,GAAG;QAE3C,gBAAgB;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,QAAQ,GAAG,CACjC,MAAM,GAAG,CAAC,CAAA,OAAQ,YAAY,MAAM;YAEtC,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,cAAc,IAAI,EAAE,cAAc,IAAI;IAC/C;IAEA,MAAM,cAAc;QAClB,eACG,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,EAC3B,OAAO,CAAC,CAAA;YACP,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAC7B;IACJ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,AAAC,sFAGT,OAH8F,eAC7F,+BACA;;kCAGJ,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACjB,6BACC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;iFAE7B,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAQ1C,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAgB,WAAU;;oCACxB,YAAY,KAAK,IAAI;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDACV,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oDAAG;oDAAO,KAAK,IAAI;;;;;;;;;;;;;oCAGzD,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;;;;;;;+BAVjB;;;;;;;;;;kCAkBd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,GAAG,KAAK,oBAAoB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,uBACtD,6LAAC;wCAEC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,AAAC,iDAGT,OAHyD,mBAAmB,SAC3E,2CACA;;4CAEL;4CACG,OAAO,WAAW;;uCAPf;;;;;;;;;;4BAYV,gCACC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,6BACC,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;iGAEnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDAEtB,6LAAC;kDAAM,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;YAQjD,eAAe,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAIH,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;gCAAgB,WAAW,AAAC,2DAC1B,OADoF,KAAK,OAAO,GAAG,iCAAiC;;kDAErI,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,iBACX,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;yGAEvB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DAEzB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAe,KAAK,OAAO,GAAG,KAAK,IAAI,GAAG;;;;;;kEACvD,6LAAC;wDAAE,WAAU;kEACV,KAAK,OAAO,GACT,AAAC,kBAAmC,OAAlB,KAAK,YAAY,IACnC,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;oCAKvB,KAAK,OAAO,kBACX,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;+BAxBF;;;;;;;;;;;;;;;;;;;;;;AAkCxB;GApPM;;QAkBkD,2KAAA,CAAA,cAAW;;;KAlB7D;uCAsPS", "debugId": null}}]}