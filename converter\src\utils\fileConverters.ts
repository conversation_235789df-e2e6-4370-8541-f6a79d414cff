// File conversion utilities

export interface ConversionResult {
  blob: Blob;
  filename: string;
  success: boolean;
  error?: string;
}

// Image conversion utilities
export const convertImage = async (
  file: File, 
  targetFormat: string, 
  quality: number = 0.9
): Promise<ConversionResult> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Handle transparency for formats that don't support it
      if (targetFormat === 'jpg' || targetFormat === 'jpeg') {
        ctx!.fillStyle = '#FFFFFF';
        ctx!.fillRect(0, 0, canvas.width, canvas.height);
      }
      
      ctx!.drawImage(img, 0, 0);
      
      const mimeType = targetFormat === 'jpg' ? 'image/jpeg' : `image/${targetFormat}`;
      
      canvas.toBlob((blob) => {
        if (blob) {
          const filename = file.name.replace(/\.[^/.]+$/, '') + '.' + targetFormat;
          resolve({
            blob,
            filename,
            success: true
          });
        } else {
          resolve({
            blob: new Blob(),
            filename: '',
            success: false,
            error: 'Failed to convert image'
          });
        }
      }, mimeType, quality);
    };
    
    img.onerror = () => {
      resolve({
        blob: new Blob(),
        filename: '',
        success: false,
        error: 'Failed to load image'
      });
    };
    
    img.src = URL.createObjectURL(file);
  });
};

// Text/Document conversion utilities
export const convertText = async (
  file: File, 
  targetFormat: string
): Promise<ConversionResult> => {
  try {
    const text = await file.text();
    let convertedContent = text;
    let mimeType = 'text/plain';
    
    // Markdown to HTML conversion
    if (targetFormat === 'html' && (file.type === 'text/markdown' || file.name.endsWith('.md'))) {
      convertedContent = markdownToHtml(text);
      mimeType = 'text/html';
    }
    // HTML to Markdown conversion
    else if (targetFormat === 'md' && file.type === 'text/html') {
      convertedContent = htmlToMarkdown(text);
      mimeType = 'text/markdown';
    }
    // Text to HTML conversion
    else if (targetFormat === 'html' && file.type === 'text/plain') {
      convertedContent = textToHtml(text);
      mimeType = 'text/html';
    }
    // HTML to Text conversion
    else if (targetFormat === 'txt' && file.type === 'text/html') {
      convertedContent = htmlToText(text);
      mimeType = 'text/plain';
    }
    // Set appropriate MIME type
    else if (targetFormat === 'html') {
      mimeType = 'text/html';
    } else if (targetFormat === 'md') {
      mimeType = 'text/markdown';
    }
    
    const blob = new Blob([convertedContent], { type: mimeType });
    const filename = file.name.replace(/\.[^/.]+$/, '') + '.' + targetFormat;
    
    return {
      blob,
      filename,
      success: true
    };
  } catch (error) {
    return {
      blob: new Blob(),
      filename: '',
      success: false,
      error: 'Failed to convert text file'
    };
  }
};

// Helper functions for text conversions
const markdownToHtml = (markdown: string): string => {
  let html = markdown
    // Headers
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // Bold and Italic
    .replace(/\*\*\*(.*)\*\*\*/gim, '<strong><em>$1</em></strong>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
    // Code blocks
    .replace(/```([^`]+)```/gim, '<pre><code>$1</code></pre>')
    .replace(/`([^`]+)`/gim, '<code>$1</code>')
    // Lists
    .replace(/^\* (.+$)/gim, '<li>$1</li>')
    .replace(/^\d+\. (.+$)/gim, '<li>$1</li>')
    // Line breaks
    .replace(/\n\n/gim, '</p><p>')
    .replace(/\n/gim, '<br>');
  
  // Wrap in basic HTML structure
  return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Converted Document</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <p>${html}</p>
</body>
</html>`;
};

const htmlToMarkdown = (html: string): string => {
  return html
    // Remove HTML structure
    .replace(/<html[^>]*>/gi, '')
    .replace(/<\/html>/gi, '')
    .replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '')
    .replace(/<body[^>]*>/gi, '')
    .replace(/<\/body>/gi, '')
    // Headers
    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n')
    // Bold and Italic
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    // Links
    .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
    // Code
    .replace(/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/gi, '```\n$1\n```')
    .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
    // Lists
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '* $1\n')
    // Paragraphs and breaks
    .replace(/<p[^>]*>/gi, '\n')
    .replace(/<\/p>/gi, '\n')
    .replace(/<br[^>]*>/gi, '\n')
    // Remove remaining HTML tags
    .replace(/<[^>]*>/g, '')
    // Clean up extra whitespace
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim();
};

const textToHtml = (text: string): string => {
  const htmlContent = text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/\n/g, '<br>');
  
  return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Converted Text</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
    </style>
</head>
<body>
    <p>${htmlContent}</p>
</body>
</html>`;
};

const htmlToText = (html: string): string => {
  return html
    .replace(/<br[^>]*>/gi, '\n')
    .replace(/<p[^>]*>/gi, '\n')
    .replace(/<\/p>/gi, '\n')
    .replace(/<[^>]*>/g, '')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim();
};

// File type detection utilities
export const getFileCategory = (file: File): string => {
  if (file.type.startsWith('image/')) return 'image';
  if (file.type.startsWith('audio/')) return 'audio';
  if (file.type.startsWith('video/')) return 'video';
  if (file.type.startsWith('text/') || file.type === 'application/pdf') return 'document';
  return 'other';
};

export const getSupportedFormats = (category: string): string[] => {
  switch (category) {
    case 'image':
      return ['png', 'jpg', 'jpeg', 'webp', 'gif'];
    case 'document':
      return ['txt', 'html', 'md'];
    case 'audio':
      return ['mp3', 'wav', 'ogg'];
    case 'video':
      return ['mp4', 'webm', 'avi'];
    default:
      return [];
  }
};
