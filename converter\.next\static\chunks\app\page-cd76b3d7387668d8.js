(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3625:(e,t,s)=>{Promise.resolve().then(s.bind(s,7322))},7322:(e,t,s)=>{"use strict";s.d(t,{default:()=>x});var a=s(5155),l=s(2115),r=s(6710),i=s(7213),n=s(227),c=s(9803),o=s(7434),d=s(9869),m=s(1154),p=s(1788),h=s(1540);let x=()=>{var e;let[t,s]=(0,l.useState)([]),[x,g]=(0,l.useState)([]),[b,j]=(0,l.useState)(!1),[u,w]=(0,l.useState)(""),v=(0,l.useCallback)(e=>{s(e.map(e=>(e.type.startsWith("image/")&&(e.preview=URL.createObjectURL(e)),e))),g([])},[]),{getRootProps:y,getInputProps:N,isDragActive:f}=(0,r.VB)({onDrop:v,multiple:!0,accept:{"image/*":[".png",".jpg",".jpeg",".gif",".bmp",".webp"],"text/*":[".txt",".md",".html",".css",".js",".json"],"application/pdf":[".pdf"],"audio/*":[".mp3",".wav",".ogg"],"video/*":[".mp4",".webm",".avi"]}}),C=async(e,t)=>new Promise(s=>{let a=document.createElement("canvas"),l=a.getContext("2d"),r=new Image;r.onload=()=>{a.width=r.width,a.height=r.height,null==l||l.drawImage(r,0,0),a.toBlob(e=>{s(e)},"jpg"===t?"image/jpeg":"image/".concat(t),.9)},r.src=URL.createObjectURL(e)}),$=async(e,t)=>{let s=await e.text();if("html"===t&&"text/markdown"===e.type){let e=s.replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/\*\*(.*)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*)\*/gim,"<em>$1</em>").replace(/\n/gim,"<br>");return new Blob(["<!DOCTYPE html><html><body>".concat(e,"</body></html>")],{type:"text/html"})}return"md"===t&&"text/html"===e.type?new Blob([s.replace(/<h1>(.*?)<\/h1>/gim,"# $1\n").replace(/<h2>(.*?)<\/h2>/gim,"## $1\n").replace(/<h3>(.*?)<\/h3>/gim,"### $1\n").replace(/<strong>(.*?)<\/strong>/gim,"**$1**").replace(/<em>(.*?)<\/em>/gim,"*$1*").replace(/<br>/gim,"\n").replace(/<[^>]*>/g,"")],{type:"text/markdown"}):new Blob([s],{type:"txt"===t?"text/plain":"html"===t?"text/html":"md"===t?"text/markdown":"text/plain"})},k=async(e,t)=>{let s;return s=e.type.startsWith("image/")?await C(e,t):e.type.startsWith("text/")||"application/pdf"===e.type?await $(e,t):new Blob([e],{type:e.type}),{name:e.name.replace(/\.[^/.]+$/,"")+"."+t,blob:s,originalName:e.name}},A=async()=>{if(u&&0!==t.length){j(!0);try{let e=await Promise.all(t.map(e=>k(e,u)));g(e)}catch(e){console.error("Conversion error:",e)}finally{j(!1)}}};return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"File Converter"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Convert your files to different formats easily"})]}),(0,a.jsxs)("div",{...y(),className:"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ".concat(f?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400"),children:[(0,a.jsx)("input",{...N()}),(0,a.jsx)(d.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-400"}),f?(0,a.jsx)("p",{className:"text-blue-600",children:"Drop the files here..."}):(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:"Drag & drop files here, or click to select"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Supports images, documents, audio, and video files"})]})]}),t.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Selected Files"}),(0,a.jsx)("div",{className:"grid gap-4",children:t.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 border rounded-lg",children:[(e=>e.startsWith("image/")?(0,a.jsx)(i.A,{className:"w-6 h-6"}):e.startsWith("audio/")?(0,a.jsx)(n.A,{className:"w-6 h-6"}):e.startsWith("video/")?(0,a.jsx)(c.A,{className:"w-6 h-6"}):(0,a.jsx)(o.A,{className:"w-6 h-6"}))(e.type),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[(e.size/1024/1024).toFixed(2)," MB • ",e.type]})]}),e.preview&&(0,a.jsx)("img",{src:e.preview,alt:"Preview",className:"w-16 h-16 object-cover rounded"})]},t))}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Convert To"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(e=>e.startsWith("image/")?["png","jpg","webp","gif"]:e.startsWith("text/")||"application/pdf"===e?["txt","html","md","pdf"]:e.startsWith("audio/")?["mp3","wav","ogg"]:e.startsWith("video/")?["mp4","webm","avi"]:[])((null==(e=t[0])?void 0:e.type)||"").map(e=>(0,a.jsxs)("button",{onClick:()=>w(e),className:"px-4 py-2 rounded-lg border transition-colors ".concat(u===e?"bg-blue-500 text-white border-blue-500":"bg-white text-gray-700 border-gray-300 hover:border-gray-400"),children:[".",e.toUpperCase()]},e))}),u&&(0,a.jsxs)("button",{onClick:A,disabled:b,className:"flex items-center space-x-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[b?(0,a.jsx)(m.A,{className:"w-5 h-5 animate-spin"}):(0,a.jsx)(p.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:b?"Converting...":"Convert Files"})]})]})]}),x.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Converted Files"}),(0,a.jsx)("button",{onClick:()=>{x.forEach(e=>{(0,h.saveAs)(e.blob,e.name)})},className:"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600",children:"Download All"})]}),(0,a.jsx)("div",{className:"grid gap-4",children:x.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-green-50",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Converted from ",e.originalName]})]}),(0,a.jsxs)("button",{onClick:()=>(e=>{(0,h.saveAs)(e.blob,e.name)})(e),className:"flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600",children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Download"})]})]},t))})]})]})}}},e=>{e.O(0,[330,441,964,358],()=>e(e.s=3625)),_N_E=e.O()}]);