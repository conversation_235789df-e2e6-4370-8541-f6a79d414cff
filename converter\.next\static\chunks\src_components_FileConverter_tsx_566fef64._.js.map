{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Documents/converter/converter/src/components/FileConverter.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, Download, FileText, Image as ImageIcon, Music, Video, Loader2, CheckCircle, AlertCircle } from 'lucide-react';\nimport { saveAs } from 'file-saver';\nimport { convertImage, convertText, getFileCategory, getSupportedFormats, ConversionResult } from '@/utils/fileConverters';\n\ninterface ConvertedFile {\n  name: string;\n  blob: Blob;\n  originalName: string;\n  success: boolean;\n  error?: string;\n}\n\ninterface FileWithPreview extends File {\n  preview?: string;\n}\n\nconst FileConverter: React.FC = () => {\n  const [files, setFiles] = useState<FileWithPreview[]>([]);\n  const [convertedFiles, setConvertedFiles] = useState<ConvertedFile[]>([]);\n  const [isConverting, setIsConverting] = useState(false);\n  const [selectedFormat, setSelectedFormat] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const filesWithPreview = acceptedFiles.map(file => {\n      const fileWithPreview = file as FileWithPreview;\n      if (file.type.startsWith('image/')) {\n        fileWithPreview.preview = URL.createObjectURL(file);\n      }\n      return fileWithPreview;\n    });\n    setFiles(filesWithPreview);\n    setConvertedFiles([]);\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    multiple: true,\n    accept: {\n      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'],\n      'text/*': ['.txt', '.md', '.html', '.css', '.js', '.json'],\n      'application/pdf': ['.pdf'],\n      'audio/*': ['.mp3', '.wav', '.ogg'],\n      'video/*': ['.mp4', '.webm', '.avi']\n    }\n  });\n\n  const getFileIcon = (fileType: string) => {\n    if (fileType.startsWith('image/')) return <ImageIcon className=\"w-6 h-6\" />;\n    if (fileType.startsWith('audio/')) return <Music className=\"w-6 h-6\" />;\n    if (fileType.startsWith('video/')) return <Video className=\"w-6 h-6\" />;\n    return <FileText className=\"w-6 h-6\" />;\n  };\n\n  const getAvailableFormats = (fileType: string) => {\n    if (fileType.startsWith('image/')) {\n      return ['png', 'jpg', 'webp', 'gif'];\n    }\n    if (fileType.startsWith('text/') || fileType === 'application/pdf') {\n      return ['txt', 'html', 'md', 'pdf'];\n    }\n    if (fileType.startsWith('audio/')) {\n      return ['mp3', 'wav', 'ogg'];\n    }\n    if (fileType.startsWith('video/')) {\n      return ['mp4', 'webm', 'avi'];\n    }\n    return [];\n  };\n\n  const convertImageFile = async (file: File, format: string): Promise<Blob> => {\n    return new Promise((resolve) => {\n      const canvas = document.createElement('canvas');\n      const ctx = canvas.getContext('2d');\n      const img = new Image();\n\n      img.onload = () => {\n        canvas.width = img.width;\n        canvas.height = img.height;\n        ctx?.drawImage(img, 0, 0);\n\n        const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;\n        canvas.toBlob((blob) => {\n          resolve(blob!);\n        }, mimeType, 0.9);\n      };\n\n      img.src = URL.createObjectURL(file);\n    });\n  };\n\n  const convertTextFile = async (file: File, format: string): Promise<Blob> => {\n    const text = await file.text();\n\n    if (format === 'html' && file.type === 'text/markdown') {\n      // Simple markdown to HTML conversion\n      const html = text\n        .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n        .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n        .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n        .replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>')\n        .replace(/\\*(.*)\\*/gim, '<em>$1</em>')\n        .replace(/\\n/gim, '<br>');\n\n      return new Blob([`<!DOCTYPE html><html><body>${html}</body></html>`], { type: 'text/html' });\n    }\n\n    if (format === 'md' && file.type === 'text/html') {\n      // Simple HTML to markdown conversion\n      const markdown = text\n        .replace(/<h1>(.*?)<\\/h1>/gim, '# $1\\n')\n        .replace(/<h2>(.*?)<\\/h2>/gim, '## $1\\n')\n        .replace(/<h3>(.*?)<\\/h3>/gim, '### $1\\n')\n        .replace(/<strong>(.*?)<\\/strong>/gim, '**$1**')\n        .replace(/<em>(.*?)<\\/em>/gim, '*$1*')\n        .replace(/<br>/gim, '\\n')\n        .replace(/<[^>]*>/g, '');\n\n      return new Blob([markdown], { type: 'text/markdown' });\n    }\n\n    const mimeType = format === 'txt' ? 'text/plain' :\n      format === 'html' ? 'text/html' :\n        format === 'md' ? 'text/markdown' : 'text/plain';\n\n    return new Blob([text], { type: mimeType });\n  };\n\n  const convertFile = async (file: File, format: string): Promise<ConvertedFile> => {\n    let convertedBlob: Blob;\n\n    if (file.type.startsWith('image/')) {\n      convertedBlob = await convertImageFile(file, format);\n    } else if (file.type.startsWith('text/') || file.type === 'application/pdf') {\n      convertedBlob = await convertTextFile(file, format);\n    } else {\n      // For audio/video files, we'll just change the extension for demo purposes\n      // In a real app, you'd use libraries like FFmpeg.js\n      convertedBlob = new Blob([file], { type: file.type });\n    }\n\n    const fileName = file.name.replace(/\\.[^/.]+$/, '') + '.' + format;\n\n    return {\n      name: fileName,\n      blob: convertedBlob,\n      originalName: file.name\n    };\n  };\n\n  const handleConvert = async () => {\n    if (!selectedFormat || files.length === 0) return;\n\n    setIsConverting(true);\n    try {\n      const converted = await Promise.all(\n        files.map(file => convertFile(file, selectedFormat))\n      );\n      setConvertedFiles(converted);\n    } catch (error) {\n      console.error('Conversion error:', error);\n    } finally {\n      setIsConverting(false);\n    }\n  };\n\n  const downloadFile = (convertedFile: ConvertedFile) => {\n    saveAs(convertedFile.blob, convertedFile.name);\n  };\n\n  const downloadAll = () => {\n    convertedFiles.forEach(file => {\n      saveAs(file.blob, file.name);\n    });\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 space-y-6\">\n      <div className=\"text-center\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">File Converter</h1>\n        <p className=\"text-gray-600\">Convert your files to different formats easily</p>\n      </div>\n\n      {/* File Upload Area */}\n      <div\n        {...getRootProps()}\n        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive\n          ? 'border-blue-500 bg-blue-50'\n          : 'border-gray-300 hover:border-gray-400'\n          }`}\n      >\n        <input {...getInputProps()} />\n        <Upload className=\"w-12 h-12 mx-auto mb-4 text-gray-400\" />\n        {isDragActive ? (\n          <p className=\"text-blue-600\">Drop the files here...</p>\n        ) : (\n          <div>\n            <p className=\"text-gray-600 mb-2\">Drag & drop files here, or click to select</p>\n            <p className=\"text-sm text-gray-400\">\n              Supports images, documents, audio, and video files\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* File List */}\n      {files.length > 0 && (\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Selected Files</h3>\n          <div className=\"grid gap-4\">\n            {files.map((file, index) => (\n              <div key={index} className=\"flex items-center space-x-4 p-4 border rounded-lg\">\n                {getFileIcon(file.type)}\n                <div className=\"flex-1\">\n                  <p className=\"font-medium\">{file.name}</p>\n                  <p className=\"text-sm text-gray-500\">\n                    {(file.size / 1024 / 1024).toFixed(2)} MB • {file.type}\n                  </p>\n                </div>\n                {file.preview && (\n                  <div className=\"w-16 h-16 bg-gray-200 rounded flex items-center justify-center\">\n                    <ImageIcon className=\"w-8 h-8 text-gray-400\" />\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Format Selection */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">Convert To</h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {getAvailableFormats(files[0]?.type || '').map((format) => (\n                <button\n                  key={format}\n                  onClick={() => setSelectedFormat(format)}\n                  className={`px-4 py-2 rounded-lg border transition-colors ${selectedFormat === format\n                    ? 'bg-blue-500 text-white border-blue-500'\n                    : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'\n                    }`}\n                >\n                  .{format.toUpperCase()}\n                </button>\n              ))}\n            </div>\n\n            {selectedFormat && (\n              <button\n                onClick={handleConvert}\n                disabled={isConverting}\n                className=\"flex items-center space-x-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isConverting ? (\n                  <Loader2 className=\"w-5 h-5 animate-spin\" />\n                ) : (\n                  <Download className=\"w-5 h-5\" />\n                )}\n                <span>{isConverting ? 'Converting...' : 'Convert Files'}</span>\n              </button>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Converted Files */}\n      {convertedFiles.length > 0 && (\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold\">Converted Files</h3>\n            <button\n              onClick={downloadAll}\n              className=\"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600\"\n            >\n              Download All\n            </button>\n          </div>\n          <div className=\"grid gap-4\">\n            {convertedFiles.map((file, index) => (\n              <div key={index} className=\"flex items-center justify-between p-4 border rounded-lg bg-green-50\">\n                <div>\n                  <p className=\"font-medium\">{file.name}</p>\n                  <p className=\"text-sm text-gray-500\">\n                    Converted from {file.originalName}\n                  </p>\n                </div>\n                <button\n                  onClick={() => downloadFile(file)}\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600\"\n                >\n                  <Download className=\"w-4 h-4\" />\n                  <span>Download</span>\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FileConverter;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAoBA,MAAM,gBAA0B;QAuNG;;IAtNjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC1B,MAAM,mBAAmB,cAAc,GAAG;sEAAC,CAAA;oBACzC,MAAM,kBAAkB;oBACxB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;wBAClC,gBAAgB,OAAO,GAAG,IAAI,eAAe,CAAC;oBAChD;oBACA,OAAO;gBACT;;YACA,SAAS;YACT,kBAAkB,EAAE;QACtB;4CAAG,EAAE;IAEL,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,UAAU;QACV,QAAQ;YACN,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;gBAAQ;aAAQ;YAC7D,UAAU;gBAAC;gBAAQ;gBAAO;gBAAS;gBAAQ;gBAAO;aAAQ;YAC1D,mBAAmB;gBAAC;aAAO;YAC3B,WAAW;gBAAC;gBAAQ;gBAAQ;aAAO;YACnC,WAAW;gBAAC;gBAAQ;gBAAS;aAAO;QACtC;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,UAAU,CAAC,WAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;QAC/D,IAAI,SAAS,UAAU,CAAC,WAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC3D,IAAI,SAAS,UAAU,CAAC,WAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC3D,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,UAAU,CAAC,WAAW;YACjC,OAAO;gBAAC;gBAAO;gBAAO;gBAAQ;aAAM;QACtC;QACA,IAAI,SAAS,UAAU,CAAC,YAAY,aAAa,mBAAmB;YAClE,OAAO;gBAAC;gBAAO;gBAAQ;gBAAM;aAAM;QACrC;QACA,IAAI,SAAS,UAAU,CAAC,WAAW;YACjC,OAAO;gBAAC;gBAAO;gBAAO;aAAM;QAC9B;QACA,IAAI,SAAS,UAAU,CAAC,WAAW;YACjC,OAAO;gBAAC;gBAAO;gBAAQ;aAAM;QAC/B;QACA,OAAO,EAAE;IACX;IAEA,MAAM,mBAAmB,OAAO,MAAY;QAC1C,OAAO,IAAI,QAAQ,CAAC;YAClB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,MAAM,IAAI;YAEhB,IAAI,MAAM,GAAG;gBACX,OAAO,KAAK,GAAG,IAAI,KAAK;gBACxB,OAAO,MAAM,GAAG,IAAI,MAAM;gBAC1B,gBAAA,0BAAA,IAAK,SAAS,CAAC,KAAK,GAAG;gBAEvB,MAAM,WAAW,WAAW,QAAQ,eAAe,AAAC,SAAe,OAAP;gBAC5D,OAAO,MAAM,CAAC,CAAC;oBACb,QAAQ;gBACV,GAAG,UAAU;YACf;YAEA,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;QAChC;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAY;QACzC,MAAM,OAAO,MAAM,KAAK,IAAI;QAE5B,IAAI,WAAW,UAAU,KAAK,IAAI,KAAK,iBAAiB;YACtD,qCAAqC;YACrC,MAAM,OAAO,KACV,OAAO,CAAC,eAAe,eACvB,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,iBAAiB,eACzB,OAAO,CAAC,mBAAmB,uBAC3B,OAAO,CAAC,eAAe,eACvB,OAAO,CAAC,SAAS;YAEpB,OAAO,IAAI,KAAK;gBAAE,8BAAkC,OAAL,MAAK;aAAgB,EAAE;gBAAE,MAAM;YAAY;QAC5F;QAEA,IAAI,WAAW,QAAQ,KAAK,IAAI,KAAK,aAAa;YAChD,qCAAqC;YACrC,MAAM,WAAW,KACd,OAAO,CAAC,sBAAsB,UAC9B,OAAO,CAAC,sBAAsB,WAC9B,OAAO,CAAC,sBAAsB,YAC9B,OAAO,CAAC,8BAA8B,UACtC,OAAO,CAAC,sBAAsB,QAC9B,OAAO,CAAC,WAAW,MACnB,OAAO,CAAC,YAAY;YAEvB,OAAO,IAAI,KAAK;gBAAC;aAAS,EAAE;gBAAE,MAAM;YAAgB;QACtD;QAEA,MAAM,WAAW,WAAW,QAAQ,eAClC,WAAW,SAAS,cAClB,WAAW,OAAO,kBAAkB;QAExC,OAAO,IAAI,KAAK;YAAC;SAAK,EAAE;YAAE,MAAM;QAAS;IAC3C;IAEA,MAAM,cAAc,OAAO,MAAY;QACrC,IAAI;QAEJ,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAClC,gBAAgB,MAAM,iBAAiB,MAAM;QAC/C,OAAO,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,KAAK,IAAI,KAAK,mBAAmB;YAC3E,gBAAgB,MAAM,gBAAgB,MAAM;QAC9C,OAAO;YACL,2EAA2E;YAC3E,oDAAoD;YACpD,gBAAgB,IAAI,KAAK;gBAAC;aAAK,EAAE;gBAAE,MAAM,KAAK,IAAI;YAAC;QACrD;QAEA,MAAM,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,MAAM,MAAM;QAE5D,OAAO;YACL,MAAM;YACN,MAAM;YACN,cAAc,KAAK,IAAI;QACzB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,kBAAkB,MAAM,MAAM,KAAK,GAAG;QAE3C,gBAAgB;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,QAAQ,GAAG,CACjC,MAAM,GAAG,CAAC,CAAA,OAAQ,YAAY,MAAM;YAEtC,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,cAAc,IAAI,EAAE,cAAc,IAAI;IAC/C;IAEA,MAAM,cAAc;QAClB,eAAe,OAAO,CAAC,CAAA;YACrB,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAC7B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,AAAC,sFAGT,OAH8F,eAC7F,+BACA;;kCAGJ,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACjB,6BACC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;iFAE7B,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAQ1C,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAgB,WAAU;;oCACxB,YAAY,KAAK,IAAI;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDACV,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oDAAG;oDAAO,KAAK,IAAI;;;;;;;;;;;;;oCAGzD,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;;;;;;;+BAVjB;;;;;;;;;;kCAkBd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAI,WAAU;0CACZ,oBAAoB,EAAA,UAAA,KAAK,CAAC,EAAE,cAAR,8BAAA,QAAU,IAAI,KAAI,IAAI,GAAG,CAAC,CAAC,uBAC9C,6LAAC;wCAEC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,AAAC,iDAGT,OAHyD,mBAAmB,SAC3E,2CACA;;4CAEL;4CACG,OAAO,WAAW;;uCAPf;;;;;;;;;;4BAYV,gCACC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,6BACC,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;iGAEnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDAEtB,6LAAC;kDAAM,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;YAQjD,eAAe,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAIH,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDAAwB;oDACnB,KAAK,YAAY;;;;;;;;;;;;;kDAGrC,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;;+BAZA;;;;;;;;;;;;;;;;;;;;;;AAqBxB;GA1RM;;QAkBkD,2KAAA,CAAA,cAAW;;;KAlB7D;uCA4RS", "debugId": null}}]}