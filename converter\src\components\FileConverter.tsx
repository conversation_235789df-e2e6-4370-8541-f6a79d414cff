'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Download, FileText, Image as ImageIcon, Music, Video, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { saveAs } from 'file-saver';
import { convertImage, convertText, getFileCategory, getSupportedFormats, ConversionResult } from '@/utils/fileConverters';

interface ConvertedFile {
  name: string;
  blob: Blob;
  originalName: string;
  success: boolean;
  error?: string;
}

interface FileWithPreview extends File {
  preview?: string;
}

const FileConverter: React.FC = () => {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [convertedFiles, setConvertedFiles] = useState<ConvertedFile[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<string>('');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const filesWithPreview = acceptedFiles.map(file => {
      const fileWithPreview = file as FileWithPreview;
      if (file.type.startsWith('image/')) {
        fileWithPreview.preview = URL.createObjectURL(file);
      }
      return fileWithPreview;
    });
    setFiles(filesWithPreview);
    setConvertedFiles([]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'],
      'text/*': ['.txt', '.md', '.html', '.css', '.js', '.json'],
      'application/pdf': ['.pdf'],
      'audio/*': ['.mp3', '.wav', '.ogg'],
      'video/*': ['.mp4', '.webm', '.avi']
    }
  });

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <ImageIcon className="w-6 h-6" />;
    if (fileType.startsWith('audio/')) return <Music className="w-6 h-6" />;
    if (fileType.startsWith('video/')) return <Video className="w-6 h-6" />;
    return <FileText className="w-6 h-6" />;
  };

  const getAvailableFormats = (file: File) => {
    const category = getFileCategory(file);
    return getSupportedFormats(category);
  };

  const convertFile = async (file: File, format: string): Promise<ConvertedFile> => {
    const category = getFileCategory(file);
    let result: ConversionResult;

    try {
      if (category === 'image') {
        result = await convertImage(file, format);
      } else if (category === 'document') {
        result = await convertText(file, format);
      } else {
        // For audio/video files, we'll just change the extension for demo purposes
        // In a real app, you'd use libraries like FFmpeg.js
        const fileName = file.name.replace(/\.[^/.]+$/, '') + '.' + format;
        result = {
          blob: new Blob([file], { type: file.type }),
          filename: fileName,
          success: true
        };
      }

      return {
        name: result.filename,
        blob: result.blob,
        originalName: file.name,
        success: result.success,
        error: result.error
      };
    } catch (error) {
      return {
        name: '',
        blob: new Blob(),
        originalName: file.name,
        success: false,
        error: 'Conversion failed'
      };
    }
  };

  const handleConvert = async () => {
    if (!selectedFormat || files.length === 0) return;

    setIsConverting(true);
    try {
      const converted = await Promise.all(
        files.map(file => convertFile(file, selectedFormat))
      );
      setConvertedFiles(converted);
    } catch (error) {
      console.error('Conversion error:', error);
    } finally {
      setIsConverting(false);
    }
  };

  const downloadFile = (convertedFile: ConvertedFile) => {
    saveAs(convertedFile.blob, convertedFile.name);
  };

  const downloadAll = () => {
    convertedFiles
      .filter(file => file.success)
      .forEach(file => {
        saveAs(file.blob, file.name);
      });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">File Converter</h1>
        <p className="text-gray-600">Convert your files to different formats easily</p>
      </div>

      {/* File Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-300 hover:border-gray-400'
          }`}
      >
        <input {...getInputProps()} />
        <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        {isDragActive ? (
          <p className="text-blue-600">Drop the files here...</p>
        ) : (
          <div>
            <p className="text-gray-600 mb-2">Drag & drop files here, or click to select</p>
            <p className="text-sm text-gray-400">
              Supports images, documents, audio, and video files
            </p>
          </div>
        )}
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Selected Files</h3>
          <div className="grid gap-4">
            {files.map((file, index) => (
              <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                {getFileIcon(file.type)}
                <div className="flex-1">
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-gray-500">
                    {(file.size / 1024 / 1024).toFixed(2)} MB • {file.type}
                  </p>
                </div>
                {file.preview && (
                  <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                    <ImageIcon className="w-8 h-8 text-gray-400" />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Format Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Convert To</h3>
            <div className="flex flex-wrap gap-2">
              {files.length > 0 && getAvailableFormats(files[0]).map((format) => (
                <button
                  key={format}
                  onClick={() => setSelectedFormat(format)}
                  className={`px-4 py-2 rounded-lg border transition-colors ${selectedFormat === format
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
                    }`}
                >
                  .{format.toUpperCase()}
                </button>
              ))}
            </div>

            {selectedFormat && (
              <button
                onClick={handleConvert}
                disabled={isConverting}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isConverting ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Download className="w-5 h-5" />
                )}
                <span>{isConverting ? 'Converting...' : 'Convert Files'}</span>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Converted Files */}
      {convertedFiles.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Converted Files</h3>
            <button
              onClick={downloadAll}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
            >
              Download All
            </button>
          </div>
          <div className="grid gap-4">
            {convertedFiles.map((file, index) => (
              <div key={index} className={`flex items-center justify-between p-4 border rounded-lg ${file.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}>
                <div className="flex items-center space-x-3">
                  {file.success ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-500" />
                  )}
                  <div>
                    <p className="font-medium">{file.success ? file.name : 'Conversion Failed'}</p>
                    <p className="text-sm text-gray-500">
                      {file.success
                        ? `Converted from ${file.originalName}`
                        : file.error || 'Unknown error occurred'
                      }
                    </p>
                  </div>
                </div>
                {file.success && (
                  <button
                    onClick={() => downloadFile(file)}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download</span>
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileConverter;
